<script type="importmap">
  {
    "imports": {
      "@google/genai": "https://esm.sh/@google/genai@^0.7.0",
      "gifenc": "https://cdn.jsdelivr.net/npm/gifenc@^1.0.3/+esm"
    }
  }
</script>
<div class="app-container">
  <header>
    <h1>Magical GIF Maker</h1>
    <p>Transform your creative ideas into stunning animated doodles with the power of AI</p>
  </header>

  <div class="input-container">
    <div class="input-field">
      <i class="fas fa-wand-magic-sparkles input-icon"></i>
      <input
        type="text"
        id="prompt-input"
        value="red car"
        placeholder="Describe your magical animation..."
      />
    </div>
    <button id="generate-button" class="generate-button">
      <span>Generate Magic</span>
      <i class="fas fa-sparkles"></i>
    </button>
  </div>

  <div class="generation-container">
    <div class="tabs">
      <button class="tab-button active" data-tab="frames">
        <i class="fas fa-images"></i>
        Frames
      </button>
      <button class="tab-button" data-tab="output">
        <i class="fas fa-play-circle"></i>
        Animation
      </button>
    </div>

    <div class="tab-content active" id="frames-content">
      <div class="frames-container" id="frames-container">
        <!-- Frames will appear here -->
      </div>
    </div>

    <div class="tab-content" id="output-content">
      <div class="result-container" id="result-container">
        <!-- Final GIF will appear here -->
      </div>
    </div>
  </div>

  <div class="status-container">
    <div id="status-display"></div>
  </div>
</div>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
