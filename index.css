@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css");
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');

:root {
  --primary-color: light-dark(#6366f1, #8b5cf6); /* Modern indigo/purple */
  --primary-light: light-dark(#a5b4fc, #c4b5fd);
  --primary-dark: light-dark(#4338ca, #7c3aed);
  --secondary-color: light-dark(#ec4899, #f472b6); /* Pink accent */
  --success-color: light-dark(#10b981, #34d399);
  --warning-color: light-dark(#f59e0b, #fbbf24);
  --text-color: light-dark(#1f2937, #f9fafb);
  --text-secondary: light-dark(#6b7280, #9ca3af);
  --background-color: light-dark(#f8fafc, #0f172a);
  --card-color: light-dark(#ffffff, #1e293b);
  --border-color: light-dark(#e5e7eb, #374151);
  --input-background: light-dark(#f9fafb, #374151);
  --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --animation-time: 0.3s;
  --border-radius: 16px;
  --border-radius-lg: 24px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background-color);
  background-image:
    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  background-attachment: fixed;
  color: var(--text-color);
  line-height: 1.6;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: hidden;
}

.app-container {
  width: 100%;
  max-width: 900px;
  background: var(--card-color);
  border-radius: var(--border-radius-lg);
  padding: 40px;
  box-shadow: var(--shadow-large);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0.5;
}

header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

h1 {
  font-family: 'Poppins', sans-serif;
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 16px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--primary-dark));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  position: relative;
  display: inline-block;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

h1::after {
  content: '✨';
  position: absolute;
  top: -10px;
  right: -30px;
  font-size: 1.5rem;
  animation: sparkleFloat 2s ease-in-out infinite;
}

@keyframes sparkleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}

header p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  font-weight: 500;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.7;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.input-field {
  display: flex;
  align-items: center;
  background: var(--input-background);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0 20px;
  transition: all var(--animation-time) ease;
  position: relative;
  overflow: hidden;
}

.input-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.5s ease;
}

.input-field:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.input-field:focus-within::before {
  left: 100%;
}

.input-icon {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-right: 15px;
  transition: transform var(--animation-time) ease;
}

.input-field:focus-within .input-icon {
  transform: scale(1.1);
}

#prompt-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 18px 0;
  font-size: 1.1rem;
  font-weight: 500;
  background: transparent;
  color: var(--text-color);
}

#prompt-input::placeholder {
  color: var(--text-secondary);
  font-weight: 400;
}

.generate-button {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 18px 32px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  transition: all var(--animation-time) ease;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.generate-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.generate-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-large);
}

.generate-button:hover::before {
  left: 100%;
}

.generate-button:active {
  transform: translateY(-1px);
}

.generate-button i {
  font-size: 1.2rem;
  transition: transform var(--animation-time) ease;
}

.generate-button:hover i {
  transform: rotate(360deg);
}

.generate-button.loading {
  background: linear-gradient(135deg, var(--text-secondary), #9ca3af);
  pointer-events: none;
  cursor: not-allowed;
}

.generate-button.loading i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.generation-container {
  background: var(--input-background);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 30px;
  min-height: 400px;
  position: relative;
  backdrop-filter: blur(10px);
}

.status-bar {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--card-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.status-text {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text::before {
  content: '⚡';
  font-size: 1.2rem;
}

.progress-bar {
  height: 8px;
  background: var(--border-color);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 20px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.tabs {
  display: flex;
  margin-bottom: 24px;
  background: var(--card-color);
  border-radius: var(--border-radius);
  padding: 4px;
  border: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  padding: 12px 24px;
  cursor: pointer;
  border: none;
  background: transparent;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-secondary);
  border-radius: calc(var(--border-radius) - 4px);
  transition: all var(--animation-time) ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-button i {
  font-size: 0.9rem;
  transition: transform var(--animation-time) ease;
}

.tab-button:hover {
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
}

.tab-button:hover i {
  transform: scale(1.1);
}

.tab-button.active {
  color: white;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  box-shadow: var(--shadow-medium);
}

.tab-button.active i {
  transform: scale(1.1);
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.frames-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.frame {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  aspect-ratio: 1;
  background: var(--card-color);
  border: 2px solid var(--border-color);
  opacity: 0;
  transform: scale(0.8) translateY(30px) rotateX(15deg);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.frame.appear {
  opacity: 1;
  transform: scale(1) translateY(0) rotateX(0deg);
}

.frame:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: var(--shadow-large);
  border-color: var(--primary-color);
}

.frame img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--animation-time) ease;
}

.frame:hover img {
  transform: scale(1.1);
}

.frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), transparent);
  opacity: 0;
  transition: opacity var(--animation-time) ease;
  pointer-events: none;
  z-index: 1;
}

.frame:hover::before {
  opacity: 1;
}

.frame-number {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 20px;
  z-index: 2;
  box-shadow: var(--shadow-light);
}

.result-container {
  display: none;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  min-height: 400px;
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  padding: 20px;
}

.result-container.appear {
  display: flex;
  opacity: 1;
  transform: scale(1) translateY(0);
}

.result-container img {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-large);
  border: 3px solid var(--border-color);
  transition: all var(--animation-time) ease;
}

.result-container img:hover {
  transform: scale(1.02);
  border-color: var(--primary-color);
}

.result-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.download-button {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: var(--shadow-medium);
  transition: all var(--animation-time) ease;
  position: relative;
  overflow: hidden;
}

.download-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.download-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-large);
}

.download-button:hover::before {
  left: 100%;
}

.download-button i {
  font-size: 1.1rem;
}

.result-info {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
  background: var(--card-color);
  padding: 12px 20px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

/* Status Display */
.status-container {
  margin-top: 24px;
  padding: 16px;
  background: var(--card-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  text-align: center;
}

#status-display {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

#status-display:not(:empty)::before {
  content: '🎨';
  font-size: 1.2rem;
}

/* Magic sparkle animations */
@keyframes sparkle {
  0%, 100% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
}

.sparkle {
  position: absolute;
  width: 24px;
  height: 24px;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  pointer-events: none;
  z-index: 10;
  animation: sparkle 1.2s forwards;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.loading-pulse {
  animation: pulse 2s infinite;
}

/* Floating elements animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .app-container {
    max-width: 700px;
    padding: 32px;
  }

  h1 {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  body {
    padding: 16px;
  }

  .app-container {
    padding: 24px;
    border-radius: var(--border-radius);
  }

  h1 {
    font-size: 2.5rem;
  }

  h1::after {
    right: -20px;
    font-size: 1.2rem;
  }

  header p {
    font-size: 1.1rem;
  }

  .input-container {
    gap: 16px;
    margin-bottom: 32px;
  }

  .generate-button {
    padding: 16px 24px;
    font-size: 1rem;
  }

  .generation-container {
    padding: 24px;
  }

  .frames-container {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
  }

  .tab-button {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 20px;
  }

  h1 {
    font-size: 2rem;
  }

  header p {
    font-size: 1rem;
  }

  .frames-container {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
  }

  .result-container {
    padding: 16px;
  }

  .download-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .frame img {
    filter: brightness(0.95);
  }

  .result-container img {
    filter: brightness(0.95);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Additional magical effects */
.app-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.03), transparent, rgba(236, 72, 153, 0.03), transparent);
  animation: rotate 20s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Hover effects for better interactivity */
.input-field:hover {
  transform: translateY(-1px);
}

.generation-container:hover {
  box-shadow: var(--shadow-large);
}

/* Success state animations */
.success-glow {
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
  border-color: var(--success-color) !important;
}

/* Loading state for frames container */
.frames-container.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Empty state styling */
.frames-container:empty::after {
  content: '🎨 Frames will appear here as they are generated...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  width: 100%;
}
